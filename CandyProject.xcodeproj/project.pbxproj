// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 55;
	objects = {

/* Begin PBXBuildFile section */
		1B13849F27E1C9B0003ECC0E /* CandyProjectApp.swift in Sources */ = {isa = PBXBuildFile; fileRef = 1B13849E27E1C9B0003ECC0E /* CandyProjectApp.swift */; };
		1B1384A127E1C9B0003ECC0E /* ContentView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 1B1384A027E1C9B0003ECC0E /* ContentView.swift */; };
		1B1384A327E1C9B3003ECC0E /* Assets.xcassets in Resources */ = {isa = PBXBuildFile; fileRef = 1B1384A227E1C9B3003ECC0E /* Assets.xcassets */; };
		1B1384B027E1C9B3003ECC0E /* CandyProjectTests.swift in Sources */ = {isa = PBXBuildFile; fileRef = 1B1384AF27E1C9B3003ECC0E /* CandyProjectTests.swift */; };
		1B1384BA27E1C9B4003ECC0E /* CandyProjectUITests.swift in Sources */ = {isa = PBXBuildFile; fileRef = 1B1384B927E1C9B4003ECC0E /* CandyProjectUITests.swift */; };
		1B1384BC27E1C9B4003ECC0E /* CandyProjectUITestsLaunchTests.swift in Sources */ = {isa = PBXBuildFile; fileRef = 1B1384BB27E1C9B4003ECC0E /* CandyProjectUITestsLaunchTests.swift */; };
		1B1384CB27E1E8F2003ECC0E /* TagCreationView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 1B1384CA27E1E8F2003ECC0E /* TagCreationView.swift */; };
		1B1384D627E2D2FD003ECC0E /* Modifiers.swift in Sources */ = {isa = PBXBuildFile; fileRef = 1B1384D527E2D2FD003ECC0E /* Modifiers.swift */; };
		1B1384DC27E31A92003ECC0E /* CloseButton.swift in Sources */ = {isa = PBXBuildFile; fileRef = 1B1384DB27E31A92003ECC0E /* CloseButton.swift */; };
		1B1384E227E33E3C003ECC0E /* NewActivityView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 1B1384E127E33E3C003ECC0E /* NewActivityView.swift */; };
		1B1384E527E36878003ECC0E /* HomeView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 1B1384E427E36878003ECC0E /* HomeView.swift */; };
		1B1384E727E36ED0003ECC0E /* AccountView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 1B1384E627E36ED0003ECC0E /* AccountView.swift */; };
		1B13859627E46085003ECC0E /* Persistence.swift in Sources */ = {isa = PBXBuildFile; fileRef = 1B13859527E46085003ECC0E /* Persistence.swift */; };
		1B1385A127E46614003ECC0E /* CandyProject.xcdatamodeld in Sources */ = {isa = PBXBuildFile; fileRef = 1B13859F27E46614003ECC0E /* CandyProject.xcdatamodeld */; };
		1B1385A427E46686003ECC0E /* Tag+CoreDataClass.swift in Sources */ = {isa = PBXBuildFile; fileRef = 1B1385A227E46686003ECC0E /* Tag+CoreDataClass.swift */; };
		1B1385A527E46686003ECC0E /* Tag+CoreDataProperties.swift in Sources */ = {isa = PBXBuildFile; fileRef = 1B1385A327E46686003ECC0E /* Tag+CoreDataProperties.swift */; };
		1B1385A827E481D3003ECC0E /* Activity+CoreDataClass.swift in Sources */ = {isa = PBXBuildFile; fileRef = 1B1385A627E481D3003ECC0E /* Activity+CoreDataClass.swift */; };
		1B1385A927E481D3003ECC0E /* Activity+CoreDataProperties.swift in Sources */ = {isa = PBXBuildFile; fileRef = 1B1385A727E481D3003ECC0E /* Activity+CoreDataProperties.swift */; };
		1B8A9CAB2806FD4000FB8DE7 /* GoogleUtilities.xcframework in Frameworks */ = {isa = PBXBuildFile; fileRef = 1B8A9CA42806FD4000FB8DE7 /* GoogleUtilities.xcframework */; };
		1B8A9CAD2806FD4000FB8DE7 /* UserMessagingPlatform.xcframework in Frameworks */ = {isa = PBXBuildFile; fileRef = 1B8A9CA52806FD4000FB8DE7 /* UserMessagingPlatform.xcframework */; };
		1B8A9CAF2806FD4000FB8DE7 /* GoogleAppMeasurementIdentitySupport.xcframework in Frameworks */ = {isa = PBXBuildFile; fileRef = 1B8A9CA62806FD4000FB8DE7 /* GoogleAppMeasurementIdentitySupport.xcframework */; };
		1B8A9CB12806FD4000FB8DE7 /* PromisesObjC.xcframework in Frameworks */ = {isa = PBXBuildFile; fileRef = 1B8A9CA72806FD4000FB8DE7 /* PromisesObjC.xcframework */; };
		1B8A9CB32806FD4000FB8DE7 /* GoogleAppMeasurement.xcframework in Frameworks */ = {isa = PBXBuildFile; fileRef = 1B8A9CA82806FD4000FB8DE7 /* GoogleAppMeasurement.xcframework */; };
		1B8A9CB52806FD4000FB8DE7 /* GoogleMobileAds.xcframework in Frameworks */ = {isa = PBXBuildFile; fileRef = 1B8A9CA92806FD4000FB8DE7 /* GoogleMobileAds.xcframework */; };
		1B8A9CB72806FD4000FB8DE7 /* nanopb.xcframework in Frameworks */ = {isa = PBXBuildFile; fileRef = 1B8A9CAA2806FD4000FB8DE7 /* nanopb.xcframework */; };
		1B8A9CBB2807048800FB8DE7 /* AppTrackingTransparency.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 1B8A9CBA2807048800FB8DE7 /* AppTrackingTransparency.framework */; };
		1B8A9CBC2807049900FB8DE7 /* GoogleMobileAds.xcframework in Frameworks */ = {isa = PBXBuildFile; fileRef = 1B8A9CA92806FD4000FB8DE7 /* GoogleMobileAds.xcframework */; };
		1B8A9CBE280705FE00FB8DE7 /* OpenAd.swift in Sources */ = {isa = PBXBuildFile; fileRef = 1B8A9CBD280705FE00FB8DE7 /* OpenAd.swift */; };
		1B8A9CC0280AFA0C00FB8DE7 /* GlobalVar.swift in Sources */ = {isa = PBXBuildFile; fileRef = 1B8A9CBF280AFA0C00FB8DE7 /* GlobalVar.swift */; };
		1B8A9CC2280C0B7D00FB8DE7 /* UserNotifications.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 1B8A9CC1280C0B7D00FB8DE7 /* UserNotifications.framework */; };
		1B8A9CC8280D4D4E00FB8DE7 /* NotificationHandler.swift in Sources */ = {isa = PBXBuildFile; fileRef = 1B8A9CC7280D4D4E00FB8DE7 /* NotificationHandler.swift */; };
		1B8A9CCA280D524600FB8DE7 /* SettingView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 1B8A9CC9280D524600FB8DE7 /* SettingView.swift */; };
		1B8A9CCC280D659600FB8DE7 /* ViewType.swift in Sources */ = {isa = PBXBuildFile; fileRef = 1B8A9CCB280D659600FB8DE7 /* ViewType.swift */; };
		1B8A9CD7280E526900FB8DE7 /* LocalNotification+CoreDataClass.swift in Sources */ = {isa = PBXBuildFile; fileRef = 1B8A9CD5280E526900FB8DE7 /* LocalNotification+CoreDataClass.swift */; };
		1B8A9CD8280E526900FB8DE7 /* LocalNotification+CoreDataProperties.swift in Sources */ = {isa = PBXBuildFile; fileRef = 1B8A9CD6280E526900FB8DE7 /* LocalNotification+CoreDataProperties.swift */; };
		1B8A9CDB2812C3F400FB8DE7 /* Visit+CoreDataClass.swift in Sources */ = {isa = PBXBuildFile; fileRef = 1B8A9CD92812C3F400FB8DE7 /* Visit+CoreDataClass.swift */; };
		1B8A9CDC2812C3F400FB8DE7 /* Visit+CoreDataProperties.swift in Sources */ = {isa = PBXBuildFile; fileRef = 1B8A9CDA2812C3F400FB8DE7 /* Visit+CoreDataProperties.swift */; };
		1B8A9CDE2812D49D00FB8DE7 /* StoreKit.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 1B8A9CDD2812D49D00FB8DE7 /* StoreKit.framework */; };
		1B8A9CE12812D4D200FB8DE7 /* ReviewHandler.swift in Sources */ = {isa = PBXBuildFile; fileRef = 1B8A9CE02812D4D200FB8DE7 /* ReviewHandler.swift */; };
		1B8A9CE32812DD4400FB8DE7 /* CloudKit.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 1B8A9CE22812DD4400FB8DE7 /* CloudKit.framework */; };
		1B8A9CE62812E79E00FB8DE7 /* PointNotificationView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 1B8A9CE52812E79E00FB8DE7 /* PointNotificationView.swift */; };
		1B8A9CE82813A72C00FB8DE7 /* StoreManager.swift in Sources */ = {isa = PBXBuildFile; fileRef = 1B8A9CE72813A72C00FB8DE7 /* StoreManager.swift */; };
		1B8A9CEB2813C66200FB8DE7 /* AppSetting+CoreDataClass.swift in Sources */ = {isa = PBXBuildFile; fileRef = 1B8A9CE92813C66200FB8DE7 /* AppSetting+CoreDataClass.swift */; };
		1B8A9CEC2813C66200FB8DE7 /* AppSetting+CoreDataProperties.swift in Sources */ = {isa = PBXBuildFile; fileRef = 1B8A9CEA2813C66200FB8DE7 /* AppSetting+CoreDataProperties.swift */; };
		1B8A9CEE2813C87300FB8DE7 /* UpgradeVIPView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 1B8A9CED2813C87300FB8DE7 /* UpgradeVIPView.swift */; };
		1B93DB9627E6DB8400FFDA96 /* LongTermGoal+CoreDataClass.swift in Sources */ = {isa = PBXBuildFile; fileRef = 1B93DB9427E6DB8400FFDA96 /* LongTermGoal+CoreDataClass.swift */; };
		1B93DB9727E6DB8400FFDA96 /* LongTermGoal+CoreDataProperties.swift in Sources */ = {isa = PBXBuildFile; fileRef = 1B93DB9527E6DB8400FFDA96 /* LongTermGoal+CoreDataProperties.swift */; };
		1B93DB9B27E8BCA100FFDA96 /* DetailView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 1B93DB9A27E8BCA100FFDA96 /* DetailView.swift */; };
		1B93DBA027EE13A300FFDA96 /* DataSource.swift in Sources */ = {isa = PBXBuildFile; fileRef = 1B93DB9F27EE13A300FFDA96 /* DataSource.swift */; };
		1B93DBBB27F1A53700FFDA96 /* RemainingPoint+CoreDataClass.swift in Sources */ = {isa = PBXBuildFile; fileRef = 1B93DBB927F1A53700FFDA96 /* RemainingPoint+CoreDataClass.swift */; };
		1B93DBBC27F1A53700FFDA96 /* RemainingPoint+CoreDataProperties.swift in Sources */ = {isa = PBXBuildFile; fileRef = 1B93DBBA27F1A53700FFDA96 /* RemainingPoint+CoreDataProperties.swift */; };
		1B93DBBE27F4972400FFDA96 /* CalendarView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 1B93DBBD27F4972400FFDA96 /* CalendarView.swift */; };
		1B93DBC027F71BA900FFDA96 /* SearchView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 1B93DBBF27F71BA900FFDA96 /* SearchView.swift */; };
		1B93DBC227F84C6500FFDA96 /* GoalView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 1B93DBC127F84C6500FFDA96 /* GoalView.swift */; };
		1B93DBC627FD410D00FFDA96 /* InfoPlist.strings in Resources */ = {isa = PBXBuildFile; fileRef = 1B93DBC427FD410D00FFDA96 /* InfoPlist.strings */; };
		1B93DBC927FD410D00FFDA96 /* Localizable.strings in Resources */ = {isa = PBXBuildFile; fileRef = 1B93DBC727FD410D00FFDA96 /* Localizable.strings */; };
		1BF71A23282A53DF0067A055 /* WidgetKit.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 1BF71A0A282A53030067A055 /* WidgetKit.framework */; };
		1BF71A24282A53DF0067A055 /* SwiftUI.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 1BF71A0C282A53030067A055 /* SwiftUI.framework */; };
		1BF71A27282A53DF0067A055 /* CandyWidget.swift in Sources */ = {isa = PBXBuildFile; fileRef = 1BF71A26282A53DF0067A055 /* CandyWidget.swift */; };
		1BF71A2A282A53E00067A055 /* Assets.xcassets in Resources */ = {isa = PBXBuildFile; fileRef = 1BF71A29282A53E00067A055 /* Assets.xcassets */; };
		1BF71A2C282A53E00067A055 /* CandyWidget.intentdefinition in Sources */ = {isa = PBXBuildFile; fileRef = 1BF71A28282A53DF0067A055 /* CandyWidget.intentdefinition */; };
		1BF71A2D282A53E00067A055 /* CandyWidget.intentdefinition in Sources */ = {isa = PBXBuildFile; fileRef = 1BF71A28282A53DF0067A055 /* CandyWidget.intentdefinition */; };
		1BF71A30282A53E00067A055 /* CandyWidgetExtension.appex in Embed App Extensions */ = {isa = PBXBuildFile; fileRef = 1BF71A22282A53DF0067A055 /* CandyWidgetExtension.appex */; settings = {ATTRIBUTES = (RemoveHeadersOnCopy, ); }; };
		1BFC89A828217BEE007DF98D /* CustomDatePicker.swift in Sources */ = {isa = PBXBuildFile; fileRef = 1BFC89A728217BEE007DF98D /* CustomDatePicker.swift */; };
		1BFC89AA28217C0C007DF98D /* DateValueData.swift in Sources */ = {isa = PBXBuildFile; fileRef = 1BFC89A928217C0C007DF98D /* DateValueData.swift */; };
		1BFC89D72823B57E007DF98D /* PreferenceView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 1BFC89D62823B57E007DF98D /* PreferenceView.swift */; };
/* End PBXBuildFile section */

/* Begin PBXContainerItemProxy section */
		1B1384AC27E1C9B3003ECC0E /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 1B13849327E1C9B0003ECC0E /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 1B13849A27E1C9B0003ECC0E;
			remoteInfo = CandyProject;
		};
		1B1384B627E1C9B4003ECC0E /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 1B13849327E1C9B0003ECC0E /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 1B13849A27E1C9B0003ECC0E;
			remoteInfo = CandyProject;
		};
		1BF71A2E282A53E00067A055 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 1B13849327E1C9B0003ECC0E /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 1BF71A21282A53DF0067A055;
			remoteInfo = CandyWidgetExtension;
		};
/* End PBXContainerItemProxy section */

/* Begin PBXCopyFilesBuildPhase section */
		1BF71A1D282A53040067A055 /* Embed App Extensions */ = {
			isa = PBXCopyFilesBuildPhase;
			buildActionMask = 2147483647;
			dstPath = "";
			dstSubfolderSpec = 13;
			files = (
				1BF71A30282A53E00067A055 /* CandyWidgetExtension.appex in Embed App Extensions */,
			);
			name = "Embed App Extensions";
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXCopyFilesBuildPhase section */

/* Begin PBXFileReference section */
		1B13849B27E1C9B0003ECC0E /* CandyProject.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = CandyProject.app; sourceTree = BUILT_PRODUCTS_DIR; };
		1B13849E27E1C9B0003ECC0E /* CandyProjectApp.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = CandyProjectApp.swift; sourceTree = "<group>"; };
		1B1384A027E1C9B0003ECC0E /* ContentView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ContentView.swift; sourceTree = "<group>"; };
		1B1384A227E1C9B3003ECC0E /* Assets.xcassets */ = {isa = PBXFileReference; lastKnownFileType = folder.assetcatalog; path = Assets.xcassets; sourceTree = "<group>"; };
		1B1384AB27E1C9B3003ECC0E /* CandyProjectTests.xctest */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; path = CandyProjectTests.xctest; sourceTree = BUILT_PRODUCTS_DIR; };
		1B1384AF27E1C9B3003ECC0E /* CandyProjectTests.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = CandyProjectTests.swift; sourceTree = "<group>"; };
		1B1384B527E1C9B3003ECC0E /* CandyProjectUITests.xctest */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; path = CandyProjectUITests.xctest; sourceTree = BUILT_PRODUCTS_DIR; };
		1B1384B927E1C9B4003ECC0E /* CandyProjectUITests.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = CandyProjectUITests.swift; sourceTree = "<group>"; };
		1B1384BB27E1C9B4003ECC0E /* CandyProjectUITestsLaunchTests.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = CandyProjectUITestsLaunchTests.swift; sourceTree = "<group>"; };
		1B1384CA27E1E8F2003ECC0E /* TagCreationView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = TagCreationView.swift; sourceTree = "<group>"; };
		1B1384D527E2D2FD003ECC0E /* Modifiers.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = Modifiers.swift; sourceTree = "<group>"; };
		1B1384DB27E31A92003ECC0E /* CloseButton.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = CloseButton.swift; sourceTree = "<group>"; };
		1B1384E127E33E3C003ECC0E /* NewActivityView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = NewActivityView.swift; sourceTree = "<group>"; };
		1B1384E427E36878003ECC0E /* HomeView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = HomeView.swift; sourceTree = "<group>"; };
		1B1384E627E36ED0003ECC0E /* AccountView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = AccountView.swift; sourceTree = "<group>"; };
		1B13859527E46085003ECC0E /* Persistence.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = Persistence.swift; sourceTree = "<group>"; };
		1B1385A027E46614003ECC0E /* CandyProject.xcdatamodel */ = {isa = PBXFileReference; lastKnownFileType = wrapper.xcdatamodel; path = CandyProject.xcdatamodel; sourceTree = "<group>"; };
		1B1385A227E46686003ECC0E /* Tag+CoreDataClass.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = "Tag+CoreDataClass.swift"; sourceTree = "<group>"; };
		1B1385A327E46686003ECC0E /* Tag+CoreDataProperties.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = "Tag+CoreDataProperties.swift"; sourceTree = "<group>"; };
		1B1385A627E481D3003ECC0E /* Activity+CoreDataClass.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = "Activity+CoreDataClass.swift"; sourceTree = "<group>"; };
		1B1385A727E481D3003ECC0E /* Activity+CoreDataProperties.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = "Activity+CoreDataProperties.swift"; sourceTree = "<group>"; };
		1B8A9CA42806FD4000FB8DE7 /* GoogleUtilities.xcframework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.xcframework; name = GoogleUtilities.xcframework; path = "third_party/GoogleMobileAdsSdkiOS-9.3.0/GoogleUtilities.xcframework"; sourceTree = "<group>"; };
		1B8A9CA52806FD4000FB8DE7 /* UserMessagingPlatform.xcframework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.xcframework; name = UserMessagingPlatform.xcframework; path = "third_party/GoogleMobileAdsSdkiOS-9.3.0/UserMessagingPlatform.xcframework"; sourceTree = "<group>"; };
		1B8A9CA62806FD4000FB8DE7 /* GoogleAppMeasurementIdentitySupport.xcframework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.xcframework; name = GoogleAppMeasurementIdentitySupport.xcframework; path = "third_party/GoogleMobileAdsSdkiOS-9.3.0/GoogleAppMeasurementIdentitySupport.xcframework"; sourceTree = "<group>"; };
		1B8A9CA72806FD4000FB8DE7 /* PromisesObjC.xcframework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.xcframework; name = PromisesObjC.xcframework; path = "third_party/GoogleMobileAdsSdkiOS-9.3.0/PromisesObjC.xcframework"; sourceTree = "<group>"; };
		1B8A9CA82806FD4000FB8DE7 /* GoogleAppMeasurement.xcframework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.xcframework; name = GoogleAppMeasurement.xcframework; path = "third_party/GoogleMobileAdsSdkiOS-9.3.0/GoogleAppMeasurement.xcframework"; sourceTree = "<group>"; };
		1B8A9CA92806FD4000FB8DE7 /* GoogleMobileAds.xcframework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.xcframework; name = GoogleMobileAds.xcframework; path = "third_party/GoogleMobileAdsSdkiOS-9.3.0/GoogleMobileAds.xcframework"; sourceTree = "<group>"; };
		1B8A9CAA2806FD4000FB8DE7 /* nanopb.xcframework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.xcframework; name = nanopb.xcframework; path = "third_party/GoogleMobileAdsSdkiOS-9.3.0/nanopb.xcframework"; sourceTree = "<group>"; };
		1B8A9CBA2807048800FB8DE7 /* AppTrackingTransparency.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = AppTrackingTransparency.framework; path = System/Library/Frameworks/AppTrackingTransparency.framework; sourceTree = SDKROOT; };
		1B8A9CBD280705FE00FB8DE7 /* OpenAd.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = OpenAd.swift; sourceTree = "<group>"; };
		1B8A9CBF280AFA0C00FB8DE7 /* GlobalVar.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = GlobalVar.swift; sourceTree = "<group>"; };
		1B8A9CC1280C0B7D00FB8DE7 /* UserNotifications.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = UserNotifications.framework; path = System/Library/Frameworks/UserNotifications.framework; sourceTree = SDKROOT; };
		1B8A9CC7280D4D4E00FB8DE7 /* NotificationHandler.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = NotificationHandler.swift; sourceTree = "<group>"; };
		1B8A9CC9280D524600FB8DE7 /* SettingView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = SettingView.swift; sourceTree = "<group>"; };
		1B8A9CCB280D659600FB8DE7 /* ViewType.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ViewType.swift; sourceTree = "<group>"; };
		1B8A9CD5280E526900FB8DE7 /* LocalNotification+CoreDataClass.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = "LocalNotification+CoreDataClass.swift"; sourceTree = "<group>"; };
		1B8A9CD6280E526900FB8DE7 /* LocalNotification+CoreDataProperties.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = "LocalNotification+CoreDataProperties.swift"; sourceTree = "<group>"; };
		1B8A9CD92812C3F400FB8DE7 /* Visit+CoreDataClass.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = "Visit+CoreDataClass.swift"; sourceTree = "<group>"; };
		1B8A9CDA2812C3F400FB8DE7 /* Visit+CoreDataProperties.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = "Visit+CoreDataProperties.swift"; sourceTree = "<group>"; };
		1B8A9CDD2812D49D00FB8DE7 /* StoreKit.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = StoreKit.framework; path = System/Library/Frameworks/StoreKit.framework; sourceTree = SDKROOT; };
		1B8A9CE02812D4D200FB8DE7 /* ReviewHandler.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ReviewHandler.swift; sourceTree = "<group>"; };
		1B8A9CE22812DD4400FB8DE7 /* CloudKit.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = CloudKit.framework; path = System/Library/Frameworks/CloudKit.framework; sourceTree = SDKROOT; };
		1B8A9CE52812E79E00FB8DE7 /* PointNotificationView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = PointNotificationView.swift; sourceTree = "<group>"; };
		1B8A9CE72813A72C00FB8DE7 /* StoreManager.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = StoreManager.swift; sourceTree = "<group>"; };
		1B8A9CE92813C66200FB8DE7 /* AppSetting+CoreDataClass.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = "AppSetting+CoreDataClass.swift"; sourceTree = "<group>"; };
		1B8A9CEA2813C66200FB8DE7 /* AppSetting+CoreDataProperties.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = "AppSetting+CoreDataProperties.swift"; sourceTree = "<group>"; };
		1B8A9CED2813C87300FB8DE7 /* UpgradeVIPView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = UpgradeVIPView.swift; sourceTree = "<group>"; };
		1B93DB9427E6DB8400FFDA96 /* LongTermGoal+CoreDataClass.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = "LongTermGoal+CoreDataClass.swift"; sourceTree = "<group>"; };
		1B93DB9527E6DB8400FFDA96 /* LongTermGoal+CoreDataProperties.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = "LongTermGoal+CoreDataProperties.swift"; sourceTree = "<group>"; };
		1B93DB9A27E8BCA100FFDA96 /* DetailView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = DetailView.swift; sourceTree = "<group>"; };
		1B93DB9F27EE13A300FFDA96 /* DataSource.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = DataSource.swift; sourceTree = "<group>"; };
		1B93DBB927F1A53700FFDA96 /* RemainingPoint+CoreDataClass.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = "RemainingPoint+CoreDataClass.swift"; sourceTree = "<group>"; };
		1B93DBBA27F1A53700FFDA96 /* RemainingPoint+CoreDataProperties.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = "RemainingPoint+CoreDataProperties.swift"; sourceTree = "<group>"; };
		1B93DBBD27F4972400FFDA96 /* CalendarView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = CalendarView.swift; sourceTree = "<group>"; };
		1B93DBBF27F71BA900FFDA96 /* SearchView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = SearchView.swift; sourceTree = "<group>"; };
		1B93DBC127F84C6500FFDA96 /* GoalView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = GoalView.swift; sourceTree = "<group>"; };
		1B93DBC327FC899800FFDA96 /* Info.plist */ = {isa = PBXFileReference; lastKnownFileType = text.plist.xml; path = Info.plist; sourceTree = "<group>"; };
		1B93DBC527FD410D00FFDA96 /* zh-Hans */ = {isa = PBXFileReference; lastKnownFileType = text.plist.strings; name = "zh-Hans"; path = "zh-Hans.lproj/InfoPlist.strings"; sourceTree = "<group>"; };
		1B93DBC827FD410D00FFDA96 /* zh-Hans */ = {isa = PBXFileReference; lastKnownFileType = text.plist.strings; name = "zh-Hans"; path = "zh-Hans.lproj/Localizable.strings"; sourceTree = "<group>"; };
		1B93DBCA27FD5BE600FFDA96 /* en */ = {isa = PBXFileReference; lastKnownFileType = text.plist.strings; name = en; path = en.lproj/Localizable.strings; sourceTree = "<group>"; };
		1BF71A0A282A53030067A055 /* WidgetKit.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = WidgetKit.framework; path = System/Library/Frameworks/WidgetKit.framework; sourceTree = SDKROOT; };
		1BF71A0C282A53030067A055 /* SwiftUI.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = SwiftUI.framework; path = System/Library/Frameworks/SwiftUI.framework; sourceTree = SDKROOT; };
		1BF71A22282A53DF0067A055 /* CandyWidgetExtension.appex */ = {isa = PBXFileReference; explicitFileType = "wrapper.app-extension"; includeInIndex = 0; path = CandyWidgetExtension.appex; sourceTree = BUILT_PRODUCTS_DIR; };
		1BF71A26282A53DF0067A055 /* CandyWidget.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = CandyWidget.swift; sourceTree = "<group>"; };
		1BF71A28282A53DF0067A055 /* CandyWidget.intentdefinition */ = {isa = PBXFileReference; lastKnownFileType = file.intentdefinition; path = CandyWidget.intentdefinition; sourceTree = "<group>"; };
		1BF71A29282A53E00067A055 /* Assets.xcassets */ = {isa = PBXFileReference; lastKnownFileType = folder.assetcatalog; path = Assets.xcassets; sourceTree = "<group>"; };
		1BF71A2B282A53E00067A055 /* Info.plist */ = {isa = PBXFileReference; lastKnownFileType = text.plist.xml; path = Info.plist; sourceTree = "<group>"; };
		1BFC89A728217BEE007DF98D /* CustomDatePicker.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = CustomDatePicker.swift; sourceTree = "<group>"; };
		1BFC89A928217C0C007DF98D /* DateValueData.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = DateValueData.swift; sourceTree = "<group>"; };
		1BFC89D62823B57E007DF98D /* PreferenceView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = PreferenceView.swift; sourceTree = "<group>"; };
		DEC53EE727F95CC40058A275 /* CandyProject.entitlements */ = {isa = PBXFileReference; lastKnownFileType = text.plist.entitlements; path = CandyProject.entitlements; sourceTree = "<group>"; };
/* End PBXFileReference section */

/* Begin PBXFrameworksBuildPhase section */
		1B13849827E1C9B0003ECC0E /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				1B8A9CB32806FD4000FB8DE7 /* GoogleAppMeasurement.xcframework in Frameworks */,
				1B8A9CC2280C0B7D00FB8DE7 /* UserNotifications.framework in Frameworks */,
				1B8A9CAD2806FD4000FB8DE7 /* UserMessagingPlatform.xcframework in Frameworks */,
				1B8A9CBB2807048800FB8DE7 /* AppTrackingTransparency.framework in Frameworks */,
				1B8A9CB52806FD4000FB8DE7 /* GoogleMobileAds.xcframework in Frameworks */,
				1B8A9CAF2806FD4000FB8DE7 /* GoogleAppMeasurementIdentitySupport.xcframework in Frameworks */,
				1B8A9CAB2806FD4000FB8DE7 /* GoogleUtilities.xcframework in Frameworks */,
				1B8A9CB12806FD4000FB8DE7 /* PromisesObjC.xcframework in Frameworks */,
				1B8A9CBC2807049900FB8DE7 /* GoogleMobileAds.xcframework in Frameworks */,
				1B8A9CE32812DD4400FB8DE7 /* CloudKit.framework in Frameworks */,
				1B8A9CB72806FD4000FB8DE7 /* nanopb.xcframework in Frameworks */,
				1B8A9CDE2812D49D00FB8DE7 /* StoreKit.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		1B1384A827E1C9B3003ECC0E /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		1B1384B227E1C9B3003ECC0E /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		1BF71A1F282A53DF0067A055 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				1BF71A24282A53DF0067A055 /* SwiftUI.framework in Frameworks */,
				1BF71A23282A53DF0067A055 /* WidgetKit.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		1B13849227E1C9B0003ECC0E = {
			isa = PBXGroup;
			children = (
				1B93DB9E27EE137D00FFDA96 /* data */,
				1B13849D27E1C9B0003ECC0E /* CandyProject */,
				1B1384AE27E1C9B3003ECC0E /* CandyProjectTests */,
				1B1384B827E1C9B4003ECC0E /* CandyProjectUITests */,
				1BF71A25282A53DF0067A055 /* CandyWidget */,
				1B13849C27E1C9B0003ECC0E /* Products */,
				1B8A9CA32806FD4000FB8DE7 /* Frameworks */,
			);
			sourceTree = "<group>";
		};
		1B13849C27E1C9B0003ECC0E /* Products */ = {
			isa = PBXGroup;
			children = (
				1B13849B27E1C9B0003ECC0E /* CandyProject.app */,
				1B1384AB27E1C9B3003ECC0E /* CandyProjectTests.xctest */,
				1B1384B527E1C9B3003ECC0E /* CandyProjectUITests.xctest */,
				1BF71A22282A53DF0067A055 /* CandyWidgetExtension.appex */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		1B13849D27E1C9B0003ECC0E /* CandyProject */ = {
			isa = PBXGroup;
			children = (
				1B8A9CDF2812D4C000FB8DE7 /* Handlers */,
				1B93DBC327FC899800FFDA96 /* Info.plist */,
				1B93DBC727FD410D00FFDA96 /* Localizable.strings */,
				1B93DBC427FD410D00FFDA96 /* InfoPlist.strings */,
				DEC53EE727F95CC40058A275 /* CandyProject.entitlements */,
				1B1384D927E31A73003ECC0E /* Components */,
				1B1384D427E2D007003ECC0E /* Styles */,
				1B13849E27E1C9B0003ECC0E /* CandyProjectApp.swift */,
				1B1384A027E1C9B0003ECC0E /* ContentView.swift */,
				1B1384A227E1C9B3003ECC0E /* Assets.xcassets */,
			);
			path = CandyProject;
			sourceTree = "<group>";
		};
		1B1384AE27E1C9B3003ECC0E /* CandyProjectTests */ = {
			isa = PBXGroup;
			children = (
				1B1384AF27E1C9B3003ECC0E /* CandyProjectTests.swift */,
			);
			path = CandyProjectTests;
			sourceTree = "<group>";
		};
		1B1384B827E1C9B4003ECC0E /* CandyProjectUITests */ = {
			isa = PBXGroup;
			children = (
				1B1384B927E1C9B4003ECC0E /* CandyProjectUITests.swift */,
				1B1384BB27E1C9B4003ECC0E /* CandyProjectUITestsLaunchTests.swift */,
			);
			path = CandyProjectUITests;
			sourceTree = "<group>";
		};
		1B1384CC27E1FD66003ECC0E /* Modals */ = {
			isa = PBXGroup;
			children = (
				1B1384CA27E1E8F2003ECC0E /* TagCreationView.swift */,
				1B1384E127E33E3C003ECC0E /* NewActivityView.swift */,
				1B93DB9A27E8BCA100FFDA96 /* DetailView.swift */,
				1B1384E627E36ED0003ECC0E /* AccountView.swift */,
				1B93DBC127F84C6500FFDA96 /* GoalView.swift */,
				1B93DBBF27F71BA900FFDA96 /* SearchView.swift */,
				1B8A9CED2813C87300FB8DE7 /* UpgradeVIPView.swift */,
			);
			path = Modals;
			sourceTree = "<group>";
		};
		1B1384D427E2D007003ECC0E /* Styles */ = {
			isa = PBXGroup;
			children = (
				1B1384D527E2D2FD003ECC0E /* Modifiers.swift */,
			);
			path = Styles;
			sourceTree = "<group>";
		};
		1B1384D927E31A73003ECC0E /* Components */ = {
			isa = PBXGroup;
			children = (
				1B1384E327E36867003ECC0E /* Views */,
				1B1384DA27E31A81003ECC0E /* Buttons */,
				1BFC89A728217BEE007DF98D /* CustomDatePicker.swift */,
			);
			path = Components;
			sourceTree = "<group>";
		};
		1B1384DA27E31A81003ECC0E /* Buttons */ = {
			isa = PBXGroup;
			children = (
				1B1384DB27E31A92003ECC0E /* CloseButton.swift */,
			);
			path = Buttons;
			sourceTree = "<group>";
		};
		1B1384E327E36867003ECC0E /* Views */ = {
			isa = PBXGroup;
			children = (
				1B8A9CE42812E78600FB8DE7 /* SubViews */,
				1B1384CC27E1FD66003ECC0E /* Modals */,
				1B1384E427E36878003ECC0E /* HomeView.swift */,
				1B93DBBD27F4972400FFDA96 /* CalendarView.swift */,
				1B8A9CC9280D524600FB8DE7 /* SettingView.swift */,
			);
			path = Views;
			sourceTree = "<group>";
		};
		1B8A9CA32806FD4000FB8DE7 /* Frameworks */ = {
			isa = PBXGroup;
			children = (
				1B8A9CE22812DD4400FB8DE7 /* CloudKit.framework */,
				1B8A9CDD2812D49D00FB8DE7 /* StoreKit.framework */,
				1B8A9CC1280C0B7D00FB8DE7 /* UserNotifications.framework */,
				1B8A9CBA2807048800FB8DE7 /* AppTrackingTransparency.framework */,
				1B8A9CA82806FD4000FB8DE7 /* GoogleAppMeasurement.xcframework */,
				1B8A9CA62806FD4000FB8DE7 /* GoogleAppMeasurementIdentitySupport.xcframework */,
				1B8A9CA92806FD4000FB8DE7 /* GoogleMobileAds.xcframework */,
				1B8A9CA42806FD4000FB8DE7 /* GoogleUtilities.xcframework */,
				1B8A9CAA2806FD4000FB8DE7 /* nanopb.xcframework */,
				1B8A9CA72806FD4000FB8DE7 /* PromisesObjC.xcframework */,
				1B8A9CA52806FD4000FB8DE7 /* UserMessagingPlatform.xcframework */,
				1BF71A0A282A53030067A055 /* WidgetKit.framework */,
				1BF71A0C282A53030067A055 /* SwiftUI.framework */,
			);
			name = Frameworks;
			sourceTree = "<group>";
		};
		1B8A9CDF2812D4C000FB8DE7 /* Handlers */ = {
			isa = PBXGroup;
			children = (
				1B8A9CBF280AFA0C00FB8DE7 /* GlobalVar.swift */,
				1B93DB9F27EE13A300FFDA96 /* DataSource.swift */,
				1B13859527E46085003ECC0E /* Persistence.swift */,
				1B8A9CC7280D4D4E00FB8DE7 /* NotificationHandler.swift */,
				1B8A9CE02812D4D200FB8DE7 /* ReviewHandler.swift */,
				1B8A9CBD280705FE00FB8DE7 /* OpenAd.swift */,
				1B8A9CE72813A72C00FB8DE7 /* StoreManager.swift */,
			);
			path = Handlers;
			sourceTree = "<group>";
		};
		1B8A9CE42812E78600FB8DE7 /* SubViews */ = {
			isa = PBXGroup;
			children = (
				1B8A9CE52812E79E00FB8DE7 /* PointNotificationView.swift */,
				1BFC89D62823B57E007DF98D /* PreferenceView.swift */,
			);
			path = SubViews;
			sourceTree = "<group>";
		};
		1B93DB9E27EE137D00FFDA96 /* data */ = {
			isa = PBXGroup;
			children = (
				1B8A9CE92813C66200FB8DE7 /* AppSetting+CoreDataClass.swift */,
				1B8A9CEA2813C66200FB8DE7 /* AppSetting+CoreDataProperties.swift */,
				1B8A9CD92812C3F400FB8DE7 /* Visit+CoreDataClass.swift */,
				1B8A9CDA2812C3F400FB8DE7 /* Visit+CoreDataProperties.swift */,
				1B8A9CD5280E526900FB8DE7 /* LocalNotification+CoreDataClass.swift */,
				1B8A9CD6280E526900FB8DE7 /* LocalNotification+CoreDataProperties.swift */,
				1B93DBB927F1A53700FFDA96 /* RemainingPoint+CoreDataClass.swift */,
				1B93DBBA27F1A53700FFDA96 /* RemainingPoint+CoreDataProperties.swift */,
				1B93DB9427E6DB8400FFDA96 /* LongTermGoal+CoreDataClass.swift */,
				1B93DB9527E6DB8400FFDA96 /* LongTermGoal+CoreDataProperties.swift */,
				1B1385A627E481D3003ECC0E /* Activity+CoreDataClass.swift */,
				1B1385A727E481D3003ECC0E /* Activity+CoreDataProperties.swift */,
				1B1385A227E46686003ECC0E /* Tag+CoreDataClass.swift */,
				1B1385A327E46686003ECC0E /* Tag+CoreDataProperties.swift */,
				1B13859F27E46614003ECC0E /* CandyProject.xcdatamodeld */,
				1BFC89A928217C0C007DF98D /* DateValueData.swift */,
				1B8A9CCB280D659600FB8DE7 /* ViewType.swift */,
			);
			path = data;
			sourceTree = "<group>";
		};
		1BF71A25282A53DF0067A055 /* CandyWidget */ = {
			isa = PBXGroup;
			children = (
				1BF71A26282A53DF0067A055 /* CandyWidget.swift */,
				1BF71A28282A53DF0067A055 /* CandyWidget.intentdefinition */,
				1BF71A29282A53E00067A055 /* Assets.xcassets */,
				1BF71A2B282A53E00067A055 /* Info.plist */,
			);
			path = CandyWidget;
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXNativeTarget section */
		1B13849A27E1C9B0003ECC0E /* CandyProject */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 1B1384BF27E1C9B4003ECC0E /* Build configuration list for PBXNativeTarget "CandyProject" */;
			buildPhases = (
				1B13849727E1C9B0003ECC0E /* Sources */,
				1B13849827E1C9B0003ECC0E /* Frameworks */,
				1B13849927E1C9B0003ECC0E /* Resources */,
				1BF71A1D282A53040067A055 /* Embed App Extensions */,
			);
			buildRules = (
			);
			dependencies = (
				1BF71A2F282A53E00067A055 /* PBXTargetDependency */,
			);
			name = CandyProject;
			packageProductDependencies = (
			);
			productName = CandyProject;
			productReference = 1B13849B27E1C9B0003ECC0E /* CandyProject.app */;
			productType = "com.apple.product-type.application";
		};
		1B1384AA27E1C9B3003ECC0E /* CandyProjectTests */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 1B1384C227E1C9B4003ECC0E /* Build configuration list for PBXNativeTarget "CandyProjectTests" */;
			buildPhases = (
				1B1384A727E1C9B3003ECC0E /* Sources */,
				1B1384A827E1C9B3003ECC0E /* Frameworks */,
				1B1384A927E1C9B3003ECC0E /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
				1B1384AD27E1C9B3003ECC0E /* PBXTargetDependency */,
			);
			name = CandyProjectTests;
			productName = CandyProjectTests;
			productReference = 1B1384AB27E1C9B3003ECC0E /* CandyProjectTests.xctest */;
			productType = "com.apple.product-type.bundle.unit-test";
		};
		1B1384B427E1C9B3003ECC0E /* CandyProjectUITests */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 1B1384C527E1C9B4003ECC0E /* Build configuration list for PBXNativeTarget "CandyProjectUITests" */;
			buildPhases = (
				1B1384B127E1C9B3003ECC0E /* Sources */,
				1B1384B227E1C9B3003ECC0E /* Frameworks */,
				1B1384B327E1C9B3003ECC0E /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
				1B1384B727E1C9B4003ECC0E /* PBXTargetDependency */,
			);
			name = CandyProjectUITests;
			productName = CandyProjectUITests;
			productReference = 1B1384B527E1C9B3003ECC0E /* CandyProjectUITests.xctest */;
			productType = "com.apple.product-type.bundle.ui-testing";
		};
		1BF71A21282A53DF0067A055 /* CandyWidgetExtension */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 1BF71A31282A53E00067A055 /* Build configuration list for PBXNativeTarget "CandyWidgetExtension" */;
			buildPhases = (
				1BF71A1E282A53DF0067A055 /* Sources */,
				1BF71A1F282A53DF0067A055 /* Frameworks */,
				1BF71A20282A53DF0067A055 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = CandyWidgetExtension;
			productName = CandyWidgetExtension;
			productReference = 1BF71A22282A53DF0067A055 /* CandyWidgetExtension.appex */;
			productType = "com.apple.product-type.app-extension";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		1B13849327E1C9B0003ECC0E /* Project object */ = {
			isa = PBXProject;
			attributes = {
				BuildIndependentTargetsInParallel = 1;
				LastSwiftUpdateCheck = 1330;
				LastUpgradeCheck = 1320;
				TargetAttributes = {
					1B13849A27E1C9B0003ECC0E = {
						CreatedOnToolsVersion = 13.2.1;
					};
					1B1384AA27E1C9B3003ECC0E = {
						CreatedOnToolsVersion = 13.2.1;
						TestTargetID = 1B13849A27E1C9B0003ECC0E;
					};
					1B1384B427E1C9B3003ECC0E = {
						CreatedOnToolsVersion = 13.2.1;
						TestTargetID = 1B13849A27E1C9B0003ECC0E;
					};
					1BF71A21282A53DF0067A055 = {
						CreatedOnToolsVersion = 13.3.1;
					};
				};
			};
			buildConfigurationList = 1B13849627E1C9B0003ECC0E /* Build configuration list for PBXProject "CandyProject" */;
			compatibilityVersion = "Xcode 13.0";
			developmentRegion = en;
			hasScannedForEncodings = 0;
			knownRegions = (
				en,
				Base,
				"zh-Hans",
			);
			mainGroup = 1B13849227E1C9B0003ECC0E;
			packageReferences = (
			);
			productRefGroup = 1B13849C27E1C9B0003ECC0E /* Products */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				1B13849A27E1C9B0003ECC0E /* CandyProject */,
				1B1384AA27E1C9B3003ECC0E /* CandyProjectTests */,
				1B1384B427E1C9B3003ECC0E /* CandyProjectUITests */,
				1BF71A21282A53DF0067A055 /* CandyWidgetExtension */,
			);
		};
/* End PBXProject section */

/* Begin PBXResourcesBuildPhase section */
		1B13849927E1C9B0003ECC0E /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				1B1384A327E1C9B3003ECC0E /* Assets.xcassets in Resources */,
				1B93DBC927FD410D00FFDA96 /* Localizable.strings in Resources */,
				1B93DBC627FD410D00FFDA96 /* InfoPlist.strings in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		1B1384A927E1C9B3003ECC0E /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		1B1384B327E1C9B3003ECC0E /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		1BF71A20282A53DF0067A055 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				1BF71A2A282A53E00067A055 /* Assets.xcassets in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		1B13849727E1C9B0003ECC0E /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				1B1385A927E481D3003ECC0E /* Activity+CoreDataProperties.swift in Sources */,
				1B93DB9B27E8BCA100FFDA96 /* DetailView.swift in Sources */,
				1B1384CB27E1E8F2003ECC0E /* TagCreationView.swift in Sources */,
				1B8A9CDC2812C3F400FB8DE7 /* Visit+CoreDataProperties.swift in Sources */,
				1B1384D627E2D2FD003ECC0E /* Modifiers.swift in Sources */,
				1B93DBBC27F1A53700FFDA96 /* RemainingPoint+CoreDataProperties.swift in Sources */,
				1B8A9CD7280E526900FB8DE7 /* LocalNotification+CoreDataClass.swift in Sources */,
				1B1384DC27E31A92003ECC0E /* CloseButton.swift in Sources */,
				1B8A9CBE280705FE00FB8DE7 /* OpenAd.swift in Sources */,
				1B93DBBE27F4972400FFDA96 /* CalendarView.swift in Sources */,
				1B1384E727E36ED0003ECC0E /* AccountView.swift in Sources */,
				1B93DBA027EE13A300FFDA96 /* DataSource.swift in Sources */,
				1B8A9CDB2812C3F400FB8DE7 /* Visit+CoreDataClass.swift in Sources */,
				1B1384E527E36878003ECC0E /* HomeView.swift in Sources */,
				1B93DBC227F84C6500FFDA96 /* GoalView.swift in Sources */,
				1B93DB9727E6DB8400FFDA96 /* LongTermGoal+CoreDataProperties.swift in Sources */,
				1B8A9CEE2813C87300FB8DE7 /* UpgradeVIPView.swift in Sources */,
				1B8A9CE62812E79E00FB8DE7 /* PointNotificationView.swift in Sources */,
				1B93DBBB27F1A53700FFDA96 /* RemainingPoint+CoreDataClass.swift in Sources */,
				1B8A9CCC280D659600FB8DE7 /* ViewType.swift in Sources */,
				1BFC89A828217BEE007DF98D /* CustomDatePicker.swift in Sources */,
				1B1385A427E46686003ECC0E /* Tag+CoreDataClass.swift in Sources */,
				1B8A9CC0280AFA0C00FB8DE7 /* GlobalVar.swift in Sources */,
				1B8A9CE12812D4D200FB8DE7 /* ReviewHandler.swift in Sources */,
				1B8A9CE82813A72C00FB8DE7 /* StoreManager.swift in Sources */,
				1B93DBC027F71BA900FFDA96 /* SearchView.swift in Sources */,
				1BF71A2D282A53E00067A055 /* CandyWidget.intentdefinition in Sources */,
				1B8A9CEB2813C66200FB8DE7 /* AppSetting+CoreDataClass.swift in Sources */,
				1B1384A127E1C9B0003ECC0E /* ContentView.swift in Sources */,
				1B8A9CD8280E526900FB8DE7 /* LocalNotification+CoreDataProperties.swift in Sources */,
				1BFC89AA28217C0C007DF98D /* DateValueData.swift in Sources */,
				1B1385A827E481D3003ECC0E /* Activity+CoreDataClass.swift in Sources */,
				1B13859627E46085003ECC0E /* Persistence.swift in Sources */,
				1B13849F27E1C9B0003ECC0E /* CandyProjectApp.swift in Sources */,
				1B8A9CCA280D524600FB8DE7 /* SettingView.swift in Sources */,
				1B93DB9627E6DB8400FFDA96 /* LongTermGoal+CoreDataClass.swift in Sources */,
				1B1385A527E46686003ECC0E /* Tag+CoreDataProperties.swift in Sources */,
				1B1384E227E33E3C003ECC0E /* NewActivityView.swift in Sources */,
				1B1385A127E46614003ECC0E /* CandyProject.xcdatamodeld in Sources */,
				1B8A9CC8280D4D4E00FB8DE7 /* NotificationHandler.swift in Sources */,
				1B8A9CEC2813C66200FB8DE7 /* AppSetting+CoreDataProperties.swift in Sources */,
				1BFC89D72823B57E007DF98D /* PreferenceView.swift in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		1B1384A727E1C9B3003ECC0E /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				1B1384B027E1C9B3003ECC0E /* CandyProjectTests.swift in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		1B1384B127E1C9B3003ECC0E /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				1B1384BC27E1C9B4003ECC0E /* CandyProjectUITestsLaunchTests.swift in Sources */,
				1B1384BA27E1C9B4003ECC0E /* CandyProjectUITests.swift in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		1BF71A1E282A53DF0067A055 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				1BF71A2C282A53E00067A055 /* CandyWidget.intentdefinition in Sources */,
				1BF71A27282A53DF0067A055 /* CandyWidget.swift in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin PBXTargetDependency section */
		1B1384AD27E1C9B3003ECC0E /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = 1B13849A27E1C9B0003ECC0E /* CandyProject */;
			targetProxy = 1B1384AC27E1C9B3003ECC0E /* PBXContainerItemProxy */;
		};
		1B1384B727E1C9B4003ECC0E /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = 1B13849A27E1C9B0003ECC0E /* CandyProject */;
			targetProxy = 1B1384B627E1C9B4003ECC0E /* PBXContainerItemProxy */;
		};
		1BF71A2F282A53E00067A055 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = 1BF71A21282A53DF0067A055 /* CandyWidgetExtension */;
			targetProxy = 1BF71A2E282A53E00067A055 /* PBXContainerItemProxy */;
		};
/* End PBXTargetDependency section */

/* Begin PBXVariantGroup section */
		1B93DBC427FD410D00FFDA96 /* InfoPlist.strings */ = {
			isa = PBXVariantGroup;
			children = (
				1B93DBC527FD410D00FFDA96 /* zh-Hans */,
			);
			name = InfoPlist.strings;
			sourceTree = "<group>";
		};
		1B93DBC727FD410D00FFDA96 /* Localizable.strings */ = {
			isa = PBXVariantGroup;
			children = (
				1B93DBC827FD410D00FFDA96 /* zh-Hans */,
				1B93DBCA27FD5BE600FFDA96 /* en */,
			);
			name = Localizable.strings;
			sourceTree = "<group>";
		};
/* End PBXVariantGroup section */

/* Begin XCBuildConfiguration section */
		1B1384BD27E1C9B4003ECC0E /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++17";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = dwarf;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				GCC_C_LANGUAGE_STANDARD = gnu11;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 15.0;
				MTL_ENABLE_DEBUG_INFO = INCLUDE_SOURCE;
				MTL_FAST_MATH = YES;
				ONLY_ACTIVE_ARCH = YES;
				SDKROOT = iphoneos;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = DEBUG;
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
			};
			name = Debug;
		};
		1B1384BE27E1C9B4003ECC0E /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++17";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				GCC_C_LANGUAGE_STANDARD = gnu11;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 15.0;
				MTL_ENABLE_DEBUG_INFO = NO;
				MTL_FAST_MATH = YES;
				SDKROOT = iphoneos;
				SWIFT_COMPILATION_MODE = wholemodule;
				SWIFT_OPTIMIZATION_LEVEL = "-O";
				VALIDATE_PRODUCT = YES;
			};
			name = Release;
		};
		1B1384C027E1C9B4003ECC0E /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_EMBED_SWIFT_STANDARD_LIBRARIES = YES;
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_ENTITLEMENTS = CandyProject/CandyProject.entitlements;
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 2;
				DEVELOPMENT_ASSET_PATHS = "\"CandyProject/Preview Content\"";
				DEVELOPMENT_TEAM = HBLD72KZGW;
				ENABLE_PREVIEWS = YES;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_FILE = CandyProject/Info.plist;
				INFOPLIST_KEY_CFBundleDisplayName = CandyYourself;
				INFOPLIST_KEY_UIApplicationSceneManifest_Generation = YES;
				INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents = YES;
				INFOPLIST_KEY_UILaunchScreen_Generation = YES;
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPad = "UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPhone = UIInterfaceOrientationPortrait;
				INFOPLIST_KEY_UIUserInterfaceStyle = Light;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 1.6;
				OTHER_LDFLAGS = "-ObjC";
				PRODUCT_BUNDLE_IDENTIFIER = com.youran.CandyProject;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = 1;
			};
			name = Debug;
		};
		1B1384C127E1C9B4003ECC0E /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_EMBED_SWIFT_STANDARD_LIBRARIES = YES;
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_ENTITLEMENTS = CandyProject/CandyProject.entitlements;
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 2;
				DEVELOPMENT_ASSET_PATHS = "\"CandyProject/Preview Content\"";
				DEVELOPMENT_TEAM = HBLD72KZGW;
				ENABLE_PREVIEWS = YES;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_FILE = CandyProject/Info.plist;
				INFOPLIST_KEY_CFBundleDisplayName = CandyYourself;
				INFOPLIST_KEY_UIApplicationSceneManifest_Generation = YES;
				INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents = YES;
				INFOPLIST_KEY_UILaunchScreen_Generation = YES;
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPad = "UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPhone = UIInterfaceOrientationPortrait;
				INFOPLIST_KEY_UIUserInterfaceStyle = Light;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 1.6;
				OTHER_LDFLAGS = "-ObjC";
				PRODUCT_BUNDLE_IDENTIFIER = com.youran.CandyProject;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = 1;
			};
			name = Release;
		};
		1B1384C327E1C9B4003ECC0E /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_EMBED_SWIFT_STANDARD_LIBRARIES = YES;
				BUNDLE_LOADER = "$(TEST_HOST)";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = HBLD72KZGW;
				GENERATE_INFOPLIST_FILE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 15.2;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.youran.CandyProjectTests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				TEST_HOST = "$(BUILT_PRODUCTS_DIR)/CandyProject.app/CandyProject";
			};
			name = Debug;
		};
		1B1384C427E1C9B4003ECC0E /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_EMBED_SWIFT_STANDARD_LIBRARIES = YES;
				BUNDLE_LOADER = "$(TEST_HOST)";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = HBLD72KZGW;
				GENERATE_INFOPLIST_FILE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 15.2;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.youran.CandyProjectTests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				TEST_HOST = "$(BUILT_PRODUCTS_DIR)/CandyProject.app/CandyProject";
			};
			name = Release;
		};
		1B1384C627E1C9B4003ECC0E /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_EMBED_SWIFT_STANDARD_LIBRARIES = YES;
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = HBLD72KZGW;
				GENERATE_INFOPLIST_FILE = YES;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.youran.CandyProjectUITests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				TEST_TARGET_NAME = CandyProject;
			};
			name = Debug;
		};
		1B1384C727E1C9B4003ECC0E /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_EMBED_SWIFT_STANDARD_LIBRARIES = YES;
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = HBLD72KZGW;
				GENERATE_INFOPLIST_FILE = YES;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.youran.CandyProjectUITests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				TEST_TARGET_NAME = CandyProject;
			};
			name = Release;
		};
		1BF71A32282A53E00067A055 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				ASSETCATALOG_COMPILER_WIDGET_BACKGROUND_COLOR_NAME = WidgetBackground;
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = HBLD72KZGW;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_FILE = CandyWidget/Info.plist;
				INFOPLIST_KEY_CFBundleDisplayName = CandyWidget;
				INFOPLIST_KEY_NSHumanReadableCopyright = "";
				IPHONEOS_DEPLOYMENT_TARGET = 15.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@executable_path/../../Frameworks",
				);
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.youran.CandyProject.CandyWidget;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SKIP_INSTALL = YES;
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Debug;
		};
		1BF71A33282A53E00067A055 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				ASSETCATALOG_COMPILER_WIDGET_BACKGROUND_COLOR_NAME = WidgetBackground;
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = HBLD72KZGW;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_FILE = CandyWidget/Info.plist;
				INFOPLIST_KEY_CFBundleDisplayName = CandyWidget;
				INFOPLIST_KEY_NSHumanReadableCopyright = "";
				IPHONEOS_DEPLOYMENT_TARGET = 15.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@executable_path/../../Frameworks",
				);
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.youran.CandyProject.CandyWidget;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SKIP_INSTALL = YES;
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		1B13849627E1C9B0003ECC0E /* Build configuration list for PBXProject "CandyProject" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				1B1384BD27E1C9B4003ECC0E /* Debug */,
				1B1384BE27E1C9B4003ECC0E /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		1B1384BF27E1C9B4003ECC0E /* Build configuration list for PBXNativeTarget "CandyProject" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				1B1384C027E1C9B4003ECC0E /* Debug */,
				1B1384C127E1C9B4003ECC0E /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		1B1384C227E1C9B4003ECC0E /* Build configuration list for PBXNativeTarget "CandyProjectTests" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				1B1384C327E1C9B4003ECC0E /* Debug */,
				1B1384C427E1C9B4003ECC0E /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		1B1384C527E1C9B4003ECC0E /* Build configuration list for PBXNativeTarget "CandyProjectUITests" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				1B1384C627E1C9B4003ECC0E /* Debug */,
				1B1384C727E1C9B4003ECC0E /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		1BF71A31282A53E00067A055 /* Build configuration list for PBXNativeTarget "CandyWidgetExtension" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				1BF71A32282A53E00067A055 /* Debug */,
				1BF71A33282A53E00067A055 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */

/* Begin XCVersionGroup section */
		1B13859F27E46614003ECC0E /* CandyProject.xcdatamodeld */ = {
			isa = XCVersionGroup;
			children = (
				1B1385A027E46614003ECC0E /* CandyProject.xcdatamodel */,
			);
			currentVersion = 1B1385A027E46614003ECC0E /* CandyProject.xcdatamodel */;
			path = CandyProject.xcdatamodeld;
			sourceTree = "<group>";
			versionGroupType = wrapper.xcdatamodel;
		};
/* End XCVersionGroup section */
	};
	rootObject = 1B13849327E1C9B0003ECC0E /* Project object */;
}
