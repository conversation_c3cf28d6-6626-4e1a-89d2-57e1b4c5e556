//
//  SafeAreaHandler.swift
//  CandyProject
//
//  Created by Augment Agent on 2025/8/16.
//

import SwiftUI

// MARK: - iPhone 16 Series Safe Area Handler
struct SafeAreaHandler {
    static func getTopSafeArea() -> CGFloat {
        guard let windowScene = UIApplication.shared.connectedScenes.first as? UIWindowScene,
              let window = windowScene.windows.first else {
            return 0
        }
        return window.safeAreaInsets.top
    }
    
    static func getBottomSafeArea() -> CGFloat {
        guard let windowScene = UIApplication.shared.connectedScenes.first as? UIWindowScene,
              let window = windowScene.windows.first else {
            return 0
        }
        return window.safeAreaInsets.bottom
    }
    
    static func getLeadingSafeArea() -> CGFloat {
        guard let windowScene = UIApplication.shared.connectedScenes.first as? UIWindowScene,
              let window = windowScene.windows.first else {
            return 0
        }
        return window.safeAreaInsets.left
    }
    
    static func getTrailingSafeArea() -> CGFloat {
        guard let windowScene = UIApplication.shared.connectedScenes.first as? UIWindowScene,
              let window = windowScene.windows.first else {
            return 0
        }
        return window.safeAreaInsets.right
    }
    
    static var isIPhone16Series: Bool {
        let screenHeight = UIScreen.main.bounds.height
        let screenWidth = UIScreen.main.bounds.width
        
        // iPhone 16 series screen dimensions
        let iPhone16Dimensions = [
            (393.0, 852.0), // iPhone 16 / iPhone 16 Pro
            (430.0, 932.0)  // iPhone 16 Plus / iPhone 16 Pro Max
        ]
        
        return iPhone16Dimensions.contains { width, height in
            (screenWidth == width && screenHeight == height) ||
            (screenWidth == height && screenHeight == width)
        }
    }
    
    static var hasDynamicIsland: Bool {
        return getTopSafeArea() > 50 // Dynamic Island devices typically have larger top safe area
    }
}

// MARK: - Safe Area Modifier
struct SafeAreaAwareModifier: ViewModifier {
    let edges: Edge.Set
    let additionalPadding: CGFloat
    
    init(edges: Edge.Set = .all, additionalPadding: CGFloat = 0) {
        self.edges = edges
        self.additionalPadding = additionalPadding
    }
    
    func body(content: Content) -> some View {
        content
            .padding(.top, edges.contains(.top) ? SafeAreaHandler.getTopSafeArea() + additionalPadding : 0)
            .padding(.bottom, edges.contains(.bottom) ? SafeAreaHandler.getBottomSafeArea() + additionalPadding : 0)
            .padding(.leading, edges.contains(.leading) ? SafeAreaHandler.getLeadingSafeArea() + additionalPadding : 0)
            .padding(.trailing, edges.contains(.trailing) ? SafeAreaHandler.getTrailingSafeArea() + additionalPadding : 0)
    }
}

// MARK: - Dynamic Island Aware Modifier
struct DynamicIslandAwareModifier: ViewModifier {
    let isVisible: Bool
    
    func body(content: Content) -> some View {
        content
            .padding(.top, isVisible && SafeAreaHandler.hasDynamicIsland ? Spacing.lg : 0)
    }
}

// MARK: - iPhone 16 Optimized Tab Bar
struct iPhone16OptimizedTabBar: ViewModifier {
    func body(content: Content) -> some View {
        content
            .padding(.bottom, SafeAreaHandler.isIPhone16Series ? Spacing.lg : Spacing.md)
            .background(.ultraThinMaterial)
            .cornerRadius(CornerRadius.xl, corners: [.topLeft, .topRight])
            .shadow(color: .black.opacity(0.1), radius: 10, x: 0, y: -5)
    }
}

// MARK: - Responsive Layout Modifier
struct ResponsiveLayoutModifier: ViewModifier {
    let compactSpacing: CGFloat
    let regularSpacing: CGFloat
    
    @Environment(\.horizontalSizeClass) var horizontalSizeClass
    
    init(compact: CGFloat = Spacing.sm, regular: CGFloat = Spacing.lg) {
        self.compactSpacing = compact
        self.regularSpacing = regular
    }
    
    func body(content: Content) -> some View {
        content
            .padding(.horizontal, horizontalSizeClass == .compact ? compactSpacing : regularSpacing)
    }
}

// MARK: - View Extensions
extension View {
    func safeAreaAware(edges: Edge.Set = .all, additionalPadding: CGFloat = 0) -> some View {
        modifier(SafeAreaAwareModifier(edges: edges, additionalPadding: additionalPadding))
    }
    
    func dynamicIslandAware(isVisible: Bool = true) -> some View {
        modifier(DynamicIslandAwareModifier(isVisible: isVisible))
    }
    
    func iPhone16OptimizedTabBar() -> some View {
        modifier(iPhone16OptimizedTabBar())
    }
    
    func responsiveLayout(compact: CGFloat = Spacing.sm, regular: CGFloat = Spacing.lg) -> some View {
        modifier(ResponsiveLayoutModifier(compact: compact, regular: regular))
    }
}

// MARK: - Screen Size Helper
struct ScreenSize {
    static let width = UIScreen.main.bounds.width
    static let height = UIScreen.main.bounds.height
    
    static var isCompact: Bool {
        return width < 400
    }
    
    static var isRegular: Bool {
        return width >= 400
    }
    
    static var isLarge: Bool {
        return width >= 430
    }
}

// MARK: - Adaptive Spacing
struct AdaptiveSpacing {
    static func horizontal() -> CGFloat {
        if ScreenSize.isCompact {
            return Spacing.md
        } else if ScreenSize.isRegular {
            return Spacing.lg
        } else {
            return Spacing.xl
        }
    }
    
    static func vertical() -> CGFloat {
        if ScreenSize.isCompact {
            return Spacing.sm
        } else if ScreenSize.isRegular {
            return Spacing.md
        } else {
            return Spacing.lg
        }
    }
    
    static func cardPadding() -> CGFloat {
        return ScreenSize.isCompact ? Spacing.sm : Spacing.md
    }
    
    static func sectionSpacing() -> CGFloat {
        return ScreenSize.isCompact ? Spacing.md : Spacing.lg
    }
}
