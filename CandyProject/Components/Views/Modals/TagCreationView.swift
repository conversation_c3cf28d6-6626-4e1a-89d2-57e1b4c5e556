//
//  TagCreationView.swift
//  CandyProject
//
//  Created by <PERSON><PERSON> on 2022/3/16.
//

import SwiftUI

struct TagCreationView: View {
    @Environment(\.managedObjectContext) private var viewContext
    @Environment(\.dismiss) private var dismiss
    @AppStorage("showCreateTag") var showCreateTag = true
    @AppStorage("actType") var actType : TagType = .pain
    @ObservedObject var dataSource: ContentDataSource
    var isEdit: Bool
    var itemTobeEdited: Tag?
    var onTagEdit: (_ tag: Tag?) -> Void
    var onTagAdd: (_ tag: Tag) -> Void
    
    @State var tagname = ""
    @State var tagpoint = 100
    var body: some View {
        ZStack{
            
            Image("bg2")
                .resizable(resizingMode: .stretch)
                .ignoresSafeArea()
            
            CloseButton()
            
            VStack {
                if (!isEdit && actType == .pain) {
                    VStack {
                        Text("Create Your Task")
                            .font(.title)
                            .foregroundStyle(.linearGradient(colors: [Color.yellow, Color("green"), Color("pink")], startPoint: .topLeading, endPoint: .bottomTrailing))
                            .shadow(color: .gray, radius: 1)   
                    }
                }
                if (!isEdit && actType == .gain) {
                    VStack {
                        Text("Create Your Candy")
                            .font(.title)
                            .foregroundStyle(.linearGradient(colors: [Color.yellow, Color("green"), Color("pink")], startPoint: .topLeading, endPoint: .bottomTrailing))
                            .shadow(color: .gray, radius: 1)
                        
                    }
                }
                
                if (isEdit && actType == .pain) {
                    VStack {
                        Text("Edit Your Task")
                            .font(.title)
                            .foregroundStyle(.linearGradient(colors: [Color.yellow, Color("green"), Color("pink")], startPoint: .topLeading, endPoint: .bottomTrailing))
                            .shadow(color: .gray, radius: 1)
                        
                    }
                }
                
                if (isEdit && actType == .gain) {
                    VStack {
                        Text("Edit Your Candy")
                            .font(.title)
                            .foregroundStyle(.linearGradient(colors: [Color.yellow, Color("green"), Color("pink")], startPoint: .topLeading, endPoint: .bottomTrailing))
                            .shadow(color: .gray, radius: 1)
                        
                    }
                }
                
                form
                    .onAppear {
                        if ((itemTobeEdited) != nil) {
                            tagname = itemTobeEdited!.name
                            tagpoint = Int(itemTobeEdited!.point)
                        }
                    }
                
            }
            .frame(maxWidth: .infinity, alignment: .topTrailing)
            .frame(maxHeight: .infinity, alignment: .topTrailing)
            .padding(.horizontal, 30.0)
            .padding(.top, 80.0)
            
            
        }
        .background(.ultraThinMaterial)
        .onTapGesture {
            UIApplication.shared.sendAction(#selector(UIResponder.resignFirstResponder), to: nil, from: nil, for: nil)
        }
        
    }
    var form: some View {
        
        Group {
            
            VStack {
                actType == .pain ?
                Text("Task Name:").modifier(FormLabelModifier()) :
                Text("Candy Name:").modifier(FormLabelModifier())
                
                
                Divider().background(Color.black)
                TextField("", text: $tagname)
                    .foregroundColor(Color.black)
                    .placeholder(when: tagname.isEmpty, placeholder: {
                        Text("eg. \(actType == .pain ? "exercise 30 minutes" : "a cup of milk tea")")
                            .foregroundColor(Color.gray)
                            .blendMode(.overlay)
                    })
                    .padding(.horizontal, 10.0)
            }.modifier(ListModifier(height: 80, color: Color.clear.opacity(0.3)))
            
            VStack {
                actType == .pain ?
                Text("Task Point:").modifier(FormLabelModifier()) :
                Text("Candy Price:").modifier(FormLabelModifier())
                
                Divider().background(Color.black)
                TextField("", value: $tagpoint, formatter: NumberFormatter())
                    .multilineTextAlignment(.center)
                    .keyboardType(.numberPad)
                
            }.modifier(ListModifier(height: 80, color: Color.clear.opacity(0.3)))
            
            if (itemTobeEdited == nil) {
                Button {
                    guard self.tagname != "" else {return}
                    let newTag = Tag(context: viewContext)
                    newTag.id = UUID()
                    newTag.name = self.tagname
                    newTag.type = actType.rawValue
                    newTag.point = Int16(self.tagpoint)
                    dataSource.addTag(tag: newTag)
                    onTagAdd(newTag)
                    showCreateTag = false;
                } label: {
                    actType == .pain ?
                    Text("Create Task").modifier(ButtonGradientModifier()) :
                    Text("Create Candy").modifier(ButtonGradientModifier())
                }.padding(.all, 20.0)
            }
            if (itemTobeEdited != nil) {
                Button {
                    guard self.tagname != "" else {return}
                    dataSource.objectWillChange.send()
                    dataSource.editTag(tagTobeEdited: itemTobeEdited!, name: tagname, point: tagpoint)
                    itemTobeEdited!.name = tagname
                    itemTobeEdited!.point = Int16(tagpoint)
                    onTagEdit(itemTobeEdited)
                    dismiss()
                } label: {
                    Text("save").modifier(ButtonGradientModifier())
                }.padding(.all, 20.0)
            }

        }
        
    }
    
    
}

extension View {
    func placeholder<Content: View>(
        when shouldShow: Bool,
        alignment: Alignment = .leading,
        @ViewBuilder placeholder: () -> Content) -> some View {
            ZStack(alignment: alignment) {
                placeholder().opacity(shouldShow ? 1 : 0)
                self
            }
        }
}
