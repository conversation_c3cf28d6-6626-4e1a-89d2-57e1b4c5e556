//
//  Persistence.swift
//  CandyProject
//
//  Created by <PERSON><PERSON> on 2022/3/18.
//

import CoreData

struct PersistenceController {
    static let shared = PersistenceController()
    
    static var preview: PersistenceController = {
        let result = PersistenceController(inMemory: true, iCloudSync: true)
        let viewContext = result.container.viewContext
        
        for _ in 0..<3 {
            let newItem = Tag(context: viewContext)
            newItem.name = "tag 1"
            newItem.id = UUID()
            newItem.type = "pain"
            newItem.point = 100
            let newItem2 = Tag(context: viewContext)
            newItem2.name = "tag 2"
            newItem2.id = UUID()
            newItem2.type = "gain"
            newItem2.point = 134
        }
        
        for _ in 0..<3 {
            let newAct = Activity(context: viewContext)
            newAct.createdAt = Date()
            newAct.point = 100
            newAct.tagType = "pain"
            newAct.id = UUID()
            newAct.tagName = "act 1"
            let newAct2 = Activity(context: viewContext)
            newAct2.createdAt = Date()
            newAct2.point = 100
            newAct2.tagType = "gain"
            newAct2.id = UUID()
            newAct2.tagName = "act 2"
        }
        
        do {
            try viewContext.save()
        } catch {
            let nsError = error as NSError
            fatalError("Unresolved error \(nsError), \(nsError.userInfo)")
        }
        return result
    }()
    
    let container: NSPersistentCloudKitContainer
    
    init(inMemory: Bool = false, iCloudSync: Bool = false) {
        container = NSPersistentCloudKitContainer(name: "CandyProject")
        
        let storeDirectory = FileManager.default.urls(for: .applicationSupportDirectory, in: .userDomainMask).first!
        
        container.viewContext.automaticallyMergesChangesFromParent = true
        container.viewContext.mergePolicy = NSMergeByPropertyObjectTrumpMergePolicy
        container.loadPersistentStores(completionHandler: { (storeDescription, error) in
            if let error = error as NSError? {
                fatalError("Unresolved error \(error), \(error.userInfo)")
            }
        })
    }
    
    
}
