
//  ContentView.swift
//  CandyProject
//
//  Created by <PERSON><PERSON> on 2022/3/16.
//

import SwiftUI
import CoreData

extension Color {
    static let backgroundPrimary = Color(.systemBackground)
}

extension View {
    func cornerRadius(_ radius: CGFloat, corners: UIRectCorner) -> some View {
        clipShape(RoundedCorner(radius: radius, corners: corners))
    }
}

struct RoundedCorner: Shape {
    var radius: CGFloat = .infinity
    var corners: UIRectCorner = .allCorners

    func path(in rect: CGRect) -> Path {
        let path = UIBezierPath(
            roundedRect: rect,
            byRoundingCorners: corners,
            cornerRadii: CGSize(width: radius, height: radius)
        )
        return Path(path.cgPath)
    }
}

struct ContentView: View {
    @Environment(\.managedObjectContext) private var viewContext
    @AppStorage("showCreatePainAct") var showCreatePainAct = false
    @AppStorage("actType") var actType : TagType = .pain
    @AppStorage("showActDetail") var showActDetail = false
    @AppStorage("currentActId") var currentActId : String = ""
    @State var mainViewName : ViewType = .home
    @ObservedObject var dataSource : ContentDataSource
    @StateObject var gVar : GlobalVar
    @ObservedObject var storeManager : StoreManager
    
    var body: some View {
        
        ZStack {
            
            switch mainViewName {
            case .home:
                HomeView(dataSource: dataSource)
                    .safeAreaInset(edge: .bottom) {
                        VStack {}
                            .frame(height: 100)
                    }
                    .background(Color.backgroundPrimary)
            case .calendar:
                CalendarView(dataSource: dataSource)
                    .background(Color.backgroundPrimary)
            case .setting:
                SettingView(dataSource: dataSource, storeManager: storeManager, gVar : gVar)
                    .background(Color.backgroundPrimary)
            }
            
            
            HStack(spacing: 0) {
                // Calendar Tab
                Button {
                    mainViewName = .calendar
                } label: {
                    VStack(spacing: 4) {
                        Image(systemName: "calendar")
                            .font(.system(size: 20, weight: .medium))
                            .foregroundColor(mainViewName == .calendar ? .blue : .gray)

                        Text("日历")
                            .font(.caption)
                            .foregroundColor(mainViewName == .calendar ? .blue : .gray)
                    }
                    .frame(maxWidth: .infinity)
                    .padding(.vertical, 8)
                    .background(
                        RoundedRectangle(cornerRadius: 12)
                            .fill(mainViewName == .calendar ? Color.blue.opacity(0.1) : Color.clear)
                    )
                }

                // Add Button (Center)
                Button {
                    actType = .pain
                    showCreatePainAct = true
                } label: {
                    ZStack {
                        Circle()
                            .fill(
                                LinearGradient(
                                    colors: [.blue, .purple],
                                    startPoint: .topLeading,
                                    endPoint: .bottomTrailing
                                )
                            )
                            .frame(width: 56, height: 56)
                            .shadow(color: .blue.opacity(0.3), radius: 8, x: 0, y: 4)

                        Image(systemName: "plus")
                            .font(.system(size: 24, weight: .semibold))
                            .foregroundColor(.white)
                    }
                }
                .frame(maxWidth: .infinity)

                // Home Tab
                Button {
                    mainViewName = .home
                } label: {
                    VStack(spacing: 4) {
                        Image(systemName: "house")
                            .font(.system(size: 20, weight: .medium))
                            .foregroundColor(mainViewName == .home ? .blue : .gray)

                        Text("首页")
                            .font(.caption)
                            .foregroundColor(mainViewName == .home ? .blue : .gray)
                    }
                    .frame(maxWidth: .infinity)
                    .padding(.vertical, 8)
                    .background(
                        RoundedRectangle(cornerRadius: 12)
                            .fill(mainViewName == .home ? Color.blue.opacity(0.1) : Color.clear)
                    )
                }

                // Profile Tab
                Button {
                    mainViewName = .setting
                } label: {
                    VStack(spacing: 4) {
                        Image(systemName: "person.circle")
                            .font(.system(size: 20, weight: .medium))
                            .foregroundColor(mainViewName == .setting ? .blue : .gray)

                        Text("我的")
                            .font(.caption)
                            .foregroundColor(mainViewName == .setting ? .blue : .gray)
                    }
                    .frame(maxWidth: .infinity)
                    .padding(.vertical, 8)
                    .background(
                        RoundedRectangle(cornerRadius: 12)
                            .fill(mainViewName == .setting ? Color.blue.opacity(0.1) : Color.clear)
                    )
                }
            }
            .padding(.horizontal, 16)
            .padding(.top, 8)
            .padding(.bottom, 24)
            .background(.ultraThinMaterial)
            .cornerRadius(20, corners: [.topLeft, .topRight])
            .shadow(color: .black.opacity(0.1), radius: 10, x: 0, y: -5)
            .frame(maxHeight: .infinity, alignment: .bottom)
            .ignoresSafeArea()
            
            if (gVar.showLoadingScreen) {
                loadingScreen
            }
        }
        .fullScreenCover(isPresented: $showCreatePainAct, content: {
            NewActivityView(predicate: NSPredicate(value: true), dataSource: dataSource)
        })
        
    }
    
    var loadingScreen: some View {
        ZStack {
            Rectangle()
                .fill(Color.white)
                .ignoresSafeArea()
            
            HStack {
                Image("logo")
                    .resizable(resizingMode: .stretch)
                    .frame(width: 80, height: 80, alignment: .bottom)
                    .clipShape(RoundedRectangle(cornerRadius: 20))
                VStack {
                    Text("Candy Yourself")
                        .font(.headline)
                    Text("loading screen footnote")
                        .font(.footnote)
                }
            }
            .frame(maxHeight: .infinity, alignment: .bottomLeading)
            .padding(20)
            
        }
    }
    

}




//struct ContentView_Previews: PreviewProvider {
//    static var previews: some View {
//        ContentView().environment(\.managedObjectContext, PersistenceController.preview.container.viewContext)
//    }
//}
